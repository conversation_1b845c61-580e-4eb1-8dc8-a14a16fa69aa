import { Badge, Table } from "@radix-ui/themes";
import { TransactionDTO } from "../data";
import classNames from "classnames";
import { CopyButton } from "@/ui-components/copy-button";
import { useTranslations } from "next-intl";

interface ColumnConfig<K extends keyof TransactionDTO = keyof TransactionDTO> {
  header: string;
  key: K;
  width?: number | string;
  className?: string;
  render?: (value: TransactionDTO[K], row: TransactionDTO) => React.ReactNode;
}

export const TransactionTable = ({ data }: { data: TransactionDTO[] }) => {
  const columns: ColumnConfig[] = [
    {
      header: "Transaction ID",
      key: "transactionId",
      render: (value) => (
        <div className="flex items-center gap-2">
          <span className="w-50">{value}</span>
          <CopyButton text={value} />
        </div>
      ),
    },
    {
      header: "Created Date",
      key: "createdDate",
      className: "text-nowrap w-50",
    },
    {
      header: "Transaction Hash",
      key: "transactionHash",
      render: (value) =>
        value ? (
          <div className="flex items-center gap-4">
            <span className="line-clamp-3">{value}</span>
            <CopyButton text={value} />
          </div>
        ) : (
          "-"
        ),
    },
    {
      header: "Details",
      key: "details",
      className: "w-50",
    },
    {
      header: "Network",
      key: "network",
      className: "text-nowrap",
    },
    {
      header: "Total Amount (SGD)",
      key: "totalAmount",
      className: "font-bold",
      render: (value) => (
        <span
          className={value.startsWith("+") ? "text-green-600" : "text-red-600"}
        >
          {value}
        </span>
      ),
    },
    {
      header: "Fee (SGD)",
      key: "fee",
      className: "font-bold",
    },
    {
      header: "Status",
      key: "status",
      render: (value) => {
        const text = value.charAt(0).toUpperCase() + value.slice(1);
        switch (value) {
          case "pending":
            return <Badge color="blue">{text}</Badge>;
          case "verified":
            return <Badge color="green">{text}</Badge>;
          case "denied":
            return <Badge color="red">{text}</Badge>;
          default:
            return "-";
        }
      },
    },
    {
      header: "Blocked Reason",
      key: "blockedReason",
      className: "w-50",
    },
  ];

  return (
    <Table.Root variant="surface" size="3">
      <Table.Header>
        <Table.Row align="center">
          {columns.map((column) => (
            <Table.ColumnHeaderCell key={column.key}>
              <span className="text-nowrap">{column.header}</span>
            </Table.ColumnHeaderCell>
          ))}
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {data.map((row) => (
          <Table.Row key={row.transactionId} align="center">
            {columns.map((column) => (
              <Table.Cell key={column.key}>
                <div className={classNames("py-1 min-w-20", column.className)}>
                  {column.render
                    ? column.render(row[column.key], row)
                    : row[column.key] || "-"}
                </div>
              </Table.Cell>
            ))}
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  );
};
