"use client";
import { Container } from "@/ui-components/layout/container";
import { InputField } from "@/ui-components/form/text-field";
import { ArrowLeftIcon } from "@radix-ui/react-icons";
import { <PERSON><PERSON>, Card, Text } from "@radix-ui/themes";
import { useCallback, useMemo, useState } from "react";
import { SelectField } from "@/ui-components/form/select-field";
import type {
  KycOnboardingMultipartFormData,
  KycVerificationRequestData,
} from "@/api/data-contracts";
import { PrimaryButton, SecondaryButton } from "@repo/ui/form-button";
import { AccountStatus } from "./account-status";
import { Link } from "@/i18n/navigation";
import { FileField } from "@/ui-components/form/file-field";
import { VerifyingIdentity } from "./verifying-identity";
import { FormSection, StepTitle } from "../../_components/form-layout";
import { useMutation, useQuery } from "@tanstack/react-query";
import service from "@/api";
import { getJsonBlob } from "@/utils/json-blob";
import { everyPropHsValue } from "@/utils/everyPropHsValue";

type FileData = Pick<
  KycOnboardingMultipartFormData,
  "documentBack" | "documentFront" | "selfie"
>;

type IdentityDocumentOption = {
  value: KycVerificationRequestData["identityDocumentType"];
  label: string;
};

export const KycSteps = () => {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [kycData, setKycData] = useState<Partial<KycVerificationRequestData>>(
    {},
  );
  const [fileData, setFileData] = useState<FileData>({});

  const { data: identityDocumentOptions = [] } = useQuery({
    queryFn: async () => {
      const res = await service.getIdentityDocumentTypes();
      const availableList = res.data?.data ?? [];
      return (
        [
          { value: "NATIONAL_ID", label: "National ID" },
          { value: "DRIVERS_LICENSE", label: "Driver's license" },
          { value: "PASSPORT", label: "Passport" },
        ] satisfies IdentityDocumentOption[]
      ).filter((item) => {
        return availableList?.some((type) => type.name === item.value);
      });
    },
    queryKey: ["identity-document-types"],
  });

  const { data: countryList } = useQuery({
    queryFn: service.getCountries,
    queryKey: ["country-list"],
  });

  const goPrevious = () => setCurrentStep((prev) => Math.max(0, prev - 1));
  const goNext = () => setCurrentStep((prev) => Math.min(4, prev + 1));

  const { mutate } = useMutation({
    mutationFn: service.submit,
    onSuccess: goNext,
  });

  const onSubmit = () => {
    if (!everyPropHsValue(kycData)) return;
    mutate({ data: getJsonBlob(kycData), ...fileData });
  };

  const onValueChange = useCallback(
    <K extends keyof KycVerificationRequestData>(
      value: KycVerificationRequestData[K] | undefined,
      key?: K,
    ) => {
      if (!key) return;
      setKycData((prev) => ({
        ...prev,
        [key]: value,
      }));
    },
    [],
  );

  const countries = useMemo(() => {
    return (
      countryList?.data?.data?.map(({ alpha2 = "", name = "" }) => ({
        value: alpha2,
        label: name,
      })) ?? []
    );
  }, [countryList?.data?.data]);

  const renderForm = () => {
    if (currentStep === 1) {
      return (
        <>
          <StepTitle
            title="Let us know more about you"
            description="ODC is legally required to collect this information"
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <InputField<KycVerificationRequestData>
                  key="firstName"
                  title="First name"
                  placeholder="Enter first name"
                  name="firstName"
                  required
                  value={kycData.firstName ?? ""}
                  onChange={onValueChange}
                />

                <InputField<KycVerificationRequestData>
                  key="lastName"
                  title="Last name"
                  placeholder="Enter last name"
                  name="lastName"
                  required
                  value={kycData.lastName ?? ""}
                  onChange={onValueChange}
                />

                <SelectField<KycVerificationRequestData>
                  key="nationality"
                  title="Nationality"
                  name="nationality"
                  placeholder="Select nationality"
                  required
                  options={countries}
                  value={kycData.nationality ?? ""}
                  onChange={onValueChange}
                />

                <SelectField<KycVerificationRequestData>
                  key="countryOfResidence"
                  title="Country of residence"
                  name="countryOfResidence"
                  placeholder="Select country of residence"
                  required
                  options={countries}
                  value={kycData.countryOfResidence ?? ""}
                  onChange={onValueChange}
                />
              </div>
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton
                disabled={
                  !(
                    kycData.firstName &&
                    kycData.lastName &&
                    kycData.nationality &&
                    kycData.countryOfResidence
                  )
                }
                onClick={goNext}
              >
                Next
              </PrimaryButton>
              <SecondaryButton>Sign out</SecondaryButton>
            </div>
          </FormSection>
        </>
      );
    }

    if (currentStep === 2) {
      return (
        <>
          <StepTitle
            title="Select identity type"
            description={
              <p>
                By selecting your ID type, you consent to the collection, use,
                and storage of your biometric information for identity
                verification. Learn more in our{" "}
                <Text asChild className="color-primary" weight="bold">
                  <Link href="/privacy">Privacy Policy</Link>
                </Text>
                .
              </p>
            }
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <SelectField<KycVerificationRequestData>
                title="ID type"
                required
                name="identityDocumentType"
                placeholder="Select ID type"
                options={identityDocumentOptions}
                value={kycData.identityDocumentType?.toString() ?? ""}
                onChange={onValueChange}
              />
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton
                disabled={kycData.identityDocumentType == undefined}
                onClick={goNext}
              >
                Next
              </PrimaryButton>
              <SecondaryButton>Sign out</SecondaryButton>
            </div>
          </FormSection>
        </>
      );
    }

    if (currentStep === 3) {
      return (
        <>
          <StepTitle
            title="Upload images"
            description="Upload pictures of your photo ID (JPEG or PNG)"
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <div className="grid grid-cols-1 gap-4">
                <Text size="3" weight="medium">
                  Ensure your ID is clear and unobstructed—no redactions,
                  watermarks, or obscured details. This helps us verify your
                  identity quickly and accurately.
                </Text>

                <FileField
                  key="documentFront"
                  title="Front side of document"
                  accept="image/jpg,image/jpeg,image/png"
                  required
                  name="documentFront"
                  value={fileData.documentFront}
                  onChange={(file) => {
                    setFileData((prev) => ({
                      ...prev,
                      documentFront: file,
                    }));
                  }}
                />
                <FileField
                  key="documentBack"
                  title="Back side of document"
                  accept="image/jpg,image/jpeg,image/png"
                  required
                  name="documentBack"
                  value={fileData.documentBack}
                  onChange={(file) => {
                    setFileData((prev) => ({
                      ...prev,
                      documentBack: file,
                    }));
                  }}
                />
              </div>
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton
                disabled={!(fileData.documentFront && fileData.documentBack)}
                onClick={goNext}
              >
                Upload
              </PrimaryButton>
              <SecondaryButton>Sign out</SecondaryButton>
            </div>
          </FormSection>
        </>
      );
    }

    if (currentStep === 4) {
      return (
        <>
          <StepTitle
            title="Upload selfie"
            description="Upload your selfie image (JPEG or PNG)"
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <div className="grid grid-cols-1 gap-4">
                <Text size="3" weight="medium">
                  Ensure your face and eyes are fully visible in the selfie.
                  Remove glasses to avoid glare.
                </Text>

                <FileField
                  key="selfie"
                  title="Selfie image"
                  accept="image/jpg,image/jpeg,image/png"
                  required
                  name="selfie"
                  value={fileData.selfie}
                  onChange={(file) => {
                    setFileData((prev) => ({
                      ...prev,
                      selfie: file,
                    }));
                  }}
                />
              </div>
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton disabled={!fileData.selfie} onClick={onSubmit}>
                Submit verification
              </PrimaryButton>
              <SecondaryButton>Sign out</SecondaryButton>
            </div>
          </FormSection>
        </>
      );
    }
  };

  if (currentStep == 0) {
    return <AccountStatus onStartVerification={goNext} />;
  }

  if (currentStep < 5) {
    return (
      <Container>
        <div className="col-span-full md:col-start-2 md:col-span-10 pt-6 md:pt-10 mb-6">
          <Button
            variant="soft"
            radius="full"
            color="gray"
            onClick={goPrevious}
          >
            <ArrowLeftIcon /> Previous Page
          </Button>
        </div>
        {renderForm()}
      </Container>
    );
  }

  return <VerifyingIdentity />;
};
