import { Link } from "@/i18n/navigation";
import { PrimaryButton, SecondaryButton } from "@repo/ui/form-button";
import { InfoLayout } from "@/ui-components/info-layout";
import { useEffect, useState } from "react";

export const VerifyingIdentity = () => {
  const [showNext, setShowNext] = useState(false);

  useEffect(() => {
    // TODO: display according to real status
    setTimeout(() => {
      setShowNext(true);
    }, 3000);
  }, []);

  if (showNext) {
    return (
      <InfoLayout
        className="py-20 md:py-10 xl:py-20"
        icon="/graphics/orange/verified.png"
        iconAlt="verified"
        title="Identity verified"
        description={
          "Your identity has been verified! You can now start exploring and depositing funds."
        }
      >
        <div className="flex justify-center">
          <PrimaryButton asChild>
            <Link href={"/dashboard"}>Continue to dashboard</Link>
          </PrimaryButton>
        </div>
      </InfoLayout>
    );
  }

  return (
    <InfoLayout
      className="py-20 md:py-10 xl:py-20"
      icon="/graphics/orange/pending.png"
      iconAlt="pending"
      title="Verifying your identity"
      description={
        "Identity verification may take some time. You’ll be notified via email once it's complete."
      }
    >
      <div className="flex justify-center">
        <SecondaryButton>Sign out</SecondaryButton>
      </div>
    </InfoLayout>
  );
};
