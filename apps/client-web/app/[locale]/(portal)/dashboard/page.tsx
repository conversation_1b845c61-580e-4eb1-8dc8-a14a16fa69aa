"use client";
import { Container } from "@/ui-components/layout/container";
import { ProfileSection } from "./profile-section";
import { ArrowRightIcon, InfoCircledIcon } from "@radix-ui/react-icons";
import { <PERSON><PERSON>, Card, Heading, Text, Tooltip } from "@radix-ui/themes";
import { AssetValues } from "./_components/asset-values";
import { TransactionTable } from "../transaction-history/_components/transaction-table";
import { mockTransactions } from "../transaction-history/data";
import { Link } from "@/i18n/navigation";
import { useAuth } from "@/hooks/use-auth";
import { SecondaryButton } from "@repo/ui/form-button";

export default function DashboardPage() {
  const { userData } = useAuth();

  if (!userData) return null;

  return (
    <main className="grow py-6 md:py-10 bg-[#F2F2F2]">
      <Container>
        <div className="section-content flex flex-col gap-y-6">
          <ProfileSection profile={userData} />
          <Card size={{ initial: "2", md: "3" }}>
            <div className="flex flex-col gap-y-4">
              {/* header */}
              <div className="flex flex-col gap-y-4 md:flex-row justify-between md:items-start">
                <div className="flex flex-col gap-y-2">
                  <div className="flex items-center">
                    <Heading as="h3" size="5" weight="bold">
                      Total balance
                    </Heading>
                    <Tooltip
                      content="Only approved transactions will be reflected here"
                      side="right"
                    >
                      <InfoCircledIcon
                        className="ml-2 text-gray-500"
                        width={16}
                        height={16}
                      />
                    </Tooltip>
                  </div>
                  <div className="flex items-center gap-x-2">
                    <Text
                      className="color-primary"
                      size={{ initial: "7", sm: "8" }}
                      weight="bold"
                    >
                      0
                    </Text>
                    <Text size="3" weight="medium">
                      USD
                    </Text>
                  </div>
                </div>

                <Button color="orange" size="3" radius="full" asChild>
                  <Link href="/deposit">Deposit funds</Link>
                </Button>
              </div>
              {/* chart */}
              <div className="flex flex-col gap-y-2">
                <Heading as="h3" size="5" weight="bold">
                  My assets
                </Heading>
                <div className="flex flex-col  gap-y-4">
                  <AssetValues
                    main={{ name: "Crypto", value: "-" }}
                    details={[
                      { name: "USDT", value: "-" },
                      { name: "USDC", value: "-" },
                    ]}
                  />
                  <AssetValues
                    main={{ name: "Cash", value: "-" }}
                    details={[{ name: "USD", value: "-" }]}
                  />
                </div>
              </div>
            </div>
          </Card>
          <Card size={{ initial: "2", md: "3" }}>
            <div className="flex flex-col gap-y-4">
              <div className="flex justify-between items-center">
                <Heading as="h3" size="4" weight="bold">
                  Recent transactions
                </Heading>
                <SecondaryButton asChild size="3">
                  <Link href="/transaction-history">
                    View more <ArrowRightIcon />
                  </Link>
                </SecondaryButton>
              </div>

              <TransactionTable data={mockTransactions.slice(0, 3)} />
            </div>
          </Card>
        </div>
      </Container>
    </main>
  );
}
