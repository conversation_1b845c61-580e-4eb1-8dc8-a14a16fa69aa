import { UserProfileDTO } from "@/api/data-contracts";
import { CopyButton } from "@/ui-components/copy-button";
import {
  Avatar,
  Badge,
  Card,
  Heading,
  Separator,
  Text,
} from "@radix-ui/themes";

const UserStatus = ({
  userStatus,
}: {
  userStatus: UserProfileDTO["userStatus"];
}) => {
  switch (userStatus) {
    case "PENDING_KYC":
      return <Badge color="blue">Pending</Badge>;
    case "ACTIVE":
      return <Badge color="green">Approved</Badge>;
    case "SUSPENDED":
      return <Badge color="red">Rejected</Badge>;
    case "PERMANENTLY_RESTRICTED":
      return <Badge color="red">Restricted</Badge>;
    default:
      return null;
  }
};

export const ProfileSection = ({
  profile = {},
}: {
  profile: UserProfileDTO;
}) => {
  const {
    profilePictureUrl,
    firstName = "",
    lastName = "",
    publicId,
    userStatus,
  } = profile;

  return (
    <Card size={{ initial: "2", md: "3" }}>
      <div className="flex flex-col md:flex-row md:items-center gap-y-4">
        <div className="flex-1 flex gap-4 items-center">
          <Avatar
            src={profilePictureUrl}
            radius="full"
            fallback={`${firstName.charAt(0)}${lastName.charAt(0)}`}
            color="gray"
          />
          <Heading as="h3" size="4" weight="bold">
            {firstName} {lastName}
          </Heading>
        </div>
        <div className="flex-1 flex flex-col gap-2">
          <Text size="2" color="gray" weight="medium">
            Profile ID
          </Text>
          <div className="flex gap-2 items-center">
            <Text size="3">{publicId}</Text>
            {publicId && <CopyButton text={publicId} />}
          </div>
        </div>
        <div className="hidden md:block">
          <Separator orientation="vertical" size="2" className="mx-6" />
        </div>
        <div className="flex-1 flex flex-col gap-2">
          <Text size="2" color="gray" weight="medium">
            Identity verification
          </Text>
          <div>
            <UserStatus userStatus={userStatus} />
          </div>
        </div>
      </div>
    </Card>
  );
};
