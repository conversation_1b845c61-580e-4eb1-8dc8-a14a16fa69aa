import { PrimaryButton } from "@repo/ui/form-button";
import { InputField } from "@/ui-components/form/text-field";
import { Card } from "@radix-ui/themes";
import { FormSection } from "../../_components/form-layout";
import { InfoCallout } from "./info-callout";
import { StepTitle } from "./step-title";
import { FileField } from "@/ui-components/form/file-field";
import { useMutation } from "@tanstack/react-query";
import service from "@/api";
import { useCallback, useState } from "react";
import { everyPropHsValue } from "@/utils/everyPropHsValue";
import { getJsonBlob } from "@/utils/json-blob";

interface BankDepositParams {
  bankName: string;
  accountNumber: string;
  amount: number;
  uploadDocument: File;
}

export const BankForm = ({ goNext }: { goNext: () => void }) => {
  const { mutate } = useMutation({
    mutationFn: service.submitFiatDeposit,
    onSuccess: goNext,
  });

  const [data, setData] = useState<Partial<BankDepositParams>>({
    bankName: undefined,
    accountNumber: undefined,
    amount: undefined,
    uploadDocument: undefined,
  });

  const onValueChange = useCallback(
    <K extends keyof BankDepositParams>(
      value: BankDepositParams[K] | undefined,
      key?: K,
    ) => {
      if (!key) return;
      setData((prev) => ({
        ...prev,
        [key]: value,
      }));
    },
    [],
  );

  const onSubmit = () => {
    if (!everyPropHsValue(data)) return;
    const { uploadDocument, ...rest } = data;
    mutate({
      data: getJsonBlob(rest),
      proofOfTransfer: uploadDocument,
    });
  };

  return (
    <FormSection>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle
          step={1}
          title="Complete bank account and deposit details below"
          className="mb-6"
        />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField<BankDepositParams>
            name="bankName"
            title="Bank name"
            placeholder="Enter bank name"
            required
            onChange={onValueChange}
          />
          <InputField<BankDepositParams>
            name="accountNumber"
            title="Bank account number"
            placeholder="Enter account number"
            required
            onChange={onValueChange}
          />

          <InputField<BankDepositParams>
            name="amount"
            title="Deposit amount (USD)"
            placeholder="Enter amount"
            remarks="Minimum deposit amount is 10,000 USD."
            required
            onChange={onValueChange}
          />

          <InfoCallout>
            The minimum deposit amount is 10,000 USD. Deposits below this amount
            will be refunded within 30 days.
          </InfoCallout>

          <InfoCallout>
            Only amounts in multiples of 10,000 USD are accepted (e.g., 10,000,
            20,000, 30,000). Deposits below this amount will be refunded within
            30 days.
          </InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle
          step={2}
          title="Login to your banking portal and make a FAST transfer with the following information"
          className="mb-6"
        />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField
            title="Recipient bank name"
            value="PT. Bank Rakyat Indonesia (Persero), TBK"
            disabled
            enableCopy
          />
          <InputField
            title="Recipient bank account number"
            value="***************"
            disabled
            enableCopy
          />
          <InputField
            className="col-span-full"
            title="Recipient name"
            value="ODC FTZ REGULATORY AUTHORITY"
            disabled
            enableCopy
          />
          <InfoCallout>
            The bank account name must match your registered name.
          </InfoCallout>
          <InfoCallout>
            The amount entered in your bank portal must match the amount
            specified in Step 1
          </InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle
          step={3}
          title="Upload the screenshot of the bank transaction confirmation screen confirm deposit"
          className="mb-6"
        />
        <FileField
          title="Upload document"
          accept="image/jpg,image/jpeg,image/png"
          required
          name="uploadDocument"
          value={data.uploadDocument}
          onChange={(file) => {
            onValueChange(file, "uploadDocument");
          }}
        />
      </Card>
      <PrimaryButton onClick={onSubmit}>Confirm deposit</PrimaryButton>
    </FormSection>
  );
};
