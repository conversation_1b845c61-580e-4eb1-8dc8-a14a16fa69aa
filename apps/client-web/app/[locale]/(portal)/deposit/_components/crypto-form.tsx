import { Card } from "@radix-ui/themes";
import { FormSection } from "../../_components/form-layout";
import { PrimaryButton } from "@repo/ui/form-button";
import { StepTitle } from "./step-title";
import { InputField } from "@/ui-components/form/text-field";
import { InfoCallout } from "./info-callout";
import service from "@/api";
import { useCallback, useState } from "react";
import { SubmitCryptoDepositRequest } from "@/api/data-contracts";
import { useMutation } from "@tanstack/react-query";
import { everyPropHsValue } from "@/utils/everyPropHsValue";

export const CryptoForm = ({ goNext }: { goNext: () => void }) => {
  const [data, setData] = useState<Partial<SubmitCryptoDepositRequest>>({
    userWalletAddress: undefined,
    amount: undefined,
    transactionHash: undefined,
  });

  const { mutate } = useMutation({
    mutationFn: service.submitCryptoDeposit,
    onSuccess: goNext,
  });

  const onSubmit = async () => {
    if (!everyPropHsValue(data)) return;
    mutate(data);
  };

  const onValueChange = useCallback(
    <K extends keyof SubmitCryptoDepositRequest>(
      value: SubmitCryptoDepositRequest[K] | undefined,
      key?: K,
    ) => {
      if (!key) return;
      setData((prev) => ({
        ...prev,
        [key]: value,
      }));
    },
    [],
  );

  return (
    <FormSection>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle
          step={1}
          title="Complete wallet address and deposit details below"
          className="mb-6"
        />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField<SubmitCryptoDepositRequest>
            name="userWalletAddress"
            title="Wallet address"
            placeholder="Enter address"
            required
            onChange={onValueChange}
          />

          <InputField<SubmitCryptoDepositRequest>
            name="amount"
            title="Deposit amount (USDT)"
            placeholder="Enter amount"
            remarks="Minimum deposit amount is 10,000 USDT."
            required
            onChange={onValueChange}
          />

          <InfoCallout>
            The minimum deposit amount is 10,000 USDT. Deposits below this
            amount will be refunded within 30 days.
          </InfoCallout>

          <InfoCallout>
            Only amounts in multiples of 10,000 are accepted (e.g., 10,000,
            20,000, 30,000). Deposits below this amount will be refunded within
            30 days.
          </InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle
          step={2}
          title="Make a FAST transfer with the following information"
          className="mb-6"
        />
        <div className="grid grid-cols-1 gap-6">
          <InputField
            title="Wallet address"
            value="******************************************"
            disabled
            enableCopy
          />
          <InfoCallout>Only send Tether (USDT) to this address.</InfoCallout>
          <InfoCallout>
            This address can only received Tether on the Tron network. Don’t
            send Tether on any other network or it may be lost.
          </InfoCallout>
          <InfoCallout>
            The amount entered in your crypto wallet must match the amount
            specified in Step 1.
          </InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle
          step={3}
          title="Enter your transaction hash number below"
          className="mb-6"
        />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField<SubmitCryptoDepositRequest>
            name="transactionHash"
            title="Transaction hash number"
            placeholder="Enter transaction hash"
            required
            onChange={onValueChange}
          />
        </div>
      </Card>
      <PrimaryButton onClick={onSubmit} disabled={!everyPropHsValue(data)}>
        Confirm deposit
      </PrimaryButton>
    </FormSection>
  );
};
